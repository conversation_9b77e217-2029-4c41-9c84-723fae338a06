# 短信事件服务实现文档

## 概述

本次实现创建了一个专门处理短信事件发送的服务，支持多种事件类型的可扩展架构。当前主要实现了黑名单事件功能，支持加入黑名单和退出黑名单两种操作。

## 架构设计

### 1. 事件类型枚举
- **SmsEventTypeEnum**: 定义了三种事件类型
  - BLACKLIST: 黑名单事件
  - UNSUBSCRIBE: 退订事件
  - RECEIPT_STATUS: 回执状态事件（暂不实现）

### 2. 黑名单相关枚举
- **BlacklistTypeEnum**: 黑名单类型
  - TD_UPLINK: 退订上行
  - CUSTOMER_APPEAL: 用户投诉
  - OTHER: 其他
- **BlacklistActionEnum**: 黑名单操作动作
  - ADD: 加入黑名单
  - REMOVE: 退出黑名单

### 3. 事件数据结构
- **BaseSmsEvent**: 基础事件类，包含事件类型、时间、ID、来源等通用字段
- **BlacklistEvent**: 黑名单事件类，继承BaseSmsEvent
- **BlacklistEventData**: 黑名单事件数据类，包含具体的业务数据
- **SmsEventMessage**: 短信事件消息类，用于队列传输

## 核心组件

### 1. 消息生产者
- **SmsEventProducer**: 重构后的生产者类，支持发送SmsEventMessage类型的消息

### 2. 事件服务
- **SmsEventService**: 事件服务接口
- **SmsEventServiceImpl**: Common模块的基础实现
- **AdminSmsEventServiceImpl**: Admin模块的具体实现，集成了消息队列发送
- **ApiSmsEventServiceImpl**: API模块的实现（暂时只记录日志）

## 集成点

### Admin模块集成
1. **MobileBlackServiceImpl**
   - create()方法: 手动创建黑名单时发送事件
   - delete()方法: 删除黑名单时发送事件
   - batchDelete()方法: 批量删除黑名单时发送事件

2. **AddBlackBySmsUplinkJob**
   - addMobileBlack()方法: 上行退订短信添加到黑名单时发送事件

### API模块集成
1. **MobileBlackServiceImpl**
   - create()方法: 外部接口创建黑名单时发送事件
   - delete()方法: 外部接口删除黑名单时发送事件
   - batchDelete()方法: 外部接口批量删除黑名单时发送事件
   - createBlackListEntry()方法: 外部接口调用添加黑名单时发送事件

## 事件来源标识

不同的操作场景使用不同的source标识：
- `admin-manual-add`: 管理后台手动添加
- `admin-manual-remove`: 管理后台手动删除
- `admin-batch-delete`: 管理后台批量删除
- `uplink-job`: 上行退订任务添加
- `api-external-add`: API外部接口添加
- `api-external-remove`: API外部接口删除

## 使用示例

### 发送黑名单事件
```java
smsEventService.sendBlacklistEvent(
    mobile,                          // 手机号
    BlacklistActionEnum.ADD,         // 操作动作
    BlacklistTypeEnum.TD_UPLINK,     // 黑名单类型
    smsTypeCode,                     // 短信类型
    reason,                          // 原因
    operator,                        // 操作人
    "uplink-job"                     // 事件来源
);
```

## 扩展性

### 添加新的事件类型
1. 在SmsEventTypeEnum中添加新的事件类型
2. 创建对应的事件数据类（如UnsubscribeEventData）
3. 在SmsEventMessage中添加创建方法
4. 在SmsEventService中添加对应的发送方法

### 添加新的黑名单类型
1. 在BlacklistTypeEnum中添加新的类型
2. 在具体的业务逻辑中使用新的类型

## 注意事项

1. **异常处理**: 所有事件发送都包含了异常处理，确保业务逻辑不会因为事件发送失败而中断
2. **日志记录**: 事件发送成功和失败都有相应的日志记录
3. **模块隔离**: Common模块提供基础接口，具体模块提供实现，保持了良好的模块隔离
4. **消息队列**: Admin模块已集成消息队列发送，API模块需要后续配置消息生产者

## 后续工作

1. 为API模块配置消息生产者，实现真正的消息队列发送
2. 实现退订事件和回执状态事件的具体逻辑
3. 添加事件消费者来处理队列中的事件消息
4. 完善监控和告警机制
