package com.xhqb.spectre.common.service;

import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;

public interface SmsEventService {

    void sendBlacklistEvent(String mobile, BlacklistActionEnum action, BlacklistTypeEnum blacklistType,
                           String smsType, String reason, String operator, String source);

    void sendUnsubscribeEvent(String mobile, String smsType, String reason, String operator, String source);
}
