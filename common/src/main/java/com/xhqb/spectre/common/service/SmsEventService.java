package com.xhqb.spectre.common.service;

import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;

/**
 * 短信事件服务接口
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface SmsEventService {
    
    /**
     * 发送黑名单事件
     * 
     * @param mobile 手机号
     * @param action 操作动作（加入/退出）
     * @param blacklistType 黑名单类型
     * @param smsType 短信类型
     * @param reason 原因
     * @param operator 操作人
     * @param source 事件来源
     */
    void sendBlacklistEvent(String mobile, BlacklistActionEnum action, BlacklistTypeEnum blacklistType,
                           String smsType, String reason, String operator, String source);
    
    /**
     * 发送退订事件
     * 
     * @param mobile 手机号
     * @param smsType 短信类型
     * @param reason 原因
     * @param operator 操作人
     * @param source 事件来源
     */
    void sendUnsubscribeEvent(String mobile, String smsType, String reason, String operator, String source);
}
