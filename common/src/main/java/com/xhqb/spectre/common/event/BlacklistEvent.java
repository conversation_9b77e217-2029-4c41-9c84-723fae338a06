package com.xhqb.spectre.common.event;

import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import com.xhqb.spectre.common.enums.SmsEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 黑名单事件
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BlacklistEvent extends BaseSmsEvent {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 黑名单操作动作（加入/退出）
     */
    private BlacklistActionEnum action;
    
    /**
     * 黑名单类型
     */
    private BlacklistTypeEnum blacklistType;
    
    /**
     * 短信类型
     */
    private String smsType;
    
    /**
     * 原因
     */
    private String reason;
    
    /**
     * 操作人
     */
    private String operator;
    
    public BlacklistEvent() {
        super(SmsEventTypeEnum.BLACKLIST);
    }
    
    /**
     * 构建加入黑名单事件
     */
    public static BlacklistEvent buildAddEvent(String mobile, BlacklistTypeEnum blacklistType, 
                                               String smsType, String reason, String operator) {
        BlacklistEvent event = new BlacklistEvent();
        event.setMobile(mobile);
        event.setAction(BlacklistActionEnum.ADD);
        event.setBlacklistType(blacklistType);
        event.setSmsType(smsType);
        event.setReason(reason);
        event.setOperator(operator);
        return event;
    }
    
    /**
     * 构建退出黑名单事件
     */
    public static BlacklistEvent buildRemoveEvent(String mobile, BlacklistTypeEnum blacklistType, 
                                                  String smsType, String reason, String operator) {
        BlacklistEvent event = new BlacklistEvent();
        event.setMobile(mobile);
        event.setAction(BlacklistActionEnum.REMOVE);
        event.setBlacklistType(blacklistType);
        event.setSmsType(smsType);
        event.setReason(reason);
        event.setOperator(operator);
        return event;
    }
}
