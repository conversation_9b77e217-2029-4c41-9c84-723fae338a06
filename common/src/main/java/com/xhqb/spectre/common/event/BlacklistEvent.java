package com.xhqb.spectre.common.event;

import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import com.xhqb.spectre.common.enums.SmsEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BlacklistEvent extends BaseSmsEvent {

    private static final long serialVersionUID = 1L;

    private String mobile;
    private BlacklistActionEnum action;
    private BlacklistTypeEnum blacklistType;
    private String smsType;
    private String reason;
    private String operator;

    public BlacklistEvent() {
        super(SmsEventTypeEnum.BLACKLIST);
    }

    public static BlacklistEvent buildAddEvent(String mobile, BlacklistTypeEnum blacklistType,
                                               String smsType, String reason, String operator) {
        BlacklistEvent event = new BlacklistEvent();
        event.setMobile(mobile);
        event.setAction(BlacklistActionEnum.ADD);
        event.setBlacklistType(blacklistType);
        event.setSmsType(smsType);
        event.setReason(reason);
        event.setOperator(operator);
        return event;
    }

    public static BlacklistEvent buildRemoveEvent(String mobile, BlacklistTypeEnum blacklistType,
                                                  String smsType, String reason, String operator) {
        BlacklistEvent event = new BlacklistEvent();
        event.setMobile(mobile);
        event.setAction(BlacklistActionEnum.REMOVE);
        event.setBlacklistType(blacklistType);
        event.setSmsType(smsType);
        event.setReason(reason);
        event.setOperator(operator);
        return event;
    }
}
