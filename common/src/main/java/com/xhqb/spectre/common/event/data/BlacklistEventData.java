package com.xhqb.spectre.common.event.data;

import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 黑名单事件数据
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlacklistEventData implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 黑名单操作动作（加入/退出）
     */
    private BlacklistActionEnum action;
    
    /**
     * 黑名单类型
     */
    private BlacklistTypeEnum blacklistType;
    
    /**
     * 短信类型
     */
    private String smsType;
    
    /**
     * 原因
     */
    private String reason;
    
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 扩展字段
     */
    private String extData;
}
