package com.xhqb.spectre.common.event;

import com.xhqb.spectre.common.enums.SmsEventTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public abstract class BaseSmsEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    private SmsEventTypeEnum eventType;
    private LocalDateTime eventTime;
    private String eventId;
    private String source;

    public BaseSmsEvent(SmsEventTypeEnum eventType) {
        this.eventType = eventType;
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
    }

    private String generateEventId() {
        return System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
