package com.xhqb.spectre.common.event;

import com.xhqb.spectre.common.enums.SmsEventTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 短信事件基础类
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public abstract class BaseSmsEvent implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 事件类型
     */
    private SmsEventTypeEnum eventType;
    
    /**
     * 事件时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 事件来源
     */
    private String source;
    
    public BaseSmsEvent(SmsEventTypeEnum eventType) {
        this.eventType = eventType;
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
    }
    
    /**
     * 生成事件ID
     */
    private String generateEventId() {
        return System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
