package com.xhqb.spectre.common.enums;

/**
 * 短信事件类型枚举
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
public enum SmsEventTypeEnum {
    
    /**
     * 黑名单事件
     */
    BLACKLIST("BLACKLIST", "黑名单事件"),
    
    /**
     * 退订事件
     */
    UNSUBSCRIBE("UNSUBSCRIBE", "退订事件"),
    
    /**
     * 回执状态事件（暂不实现）
     */
    RECEIPT_STATUS("RECEIPT_STATUS", "回执状态事件");
    
    private final String code;
    private final String name;
    
    SmsEventTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static SmsEventTypeEnum getByCode(String code) {
        for (SmsEventTypeEnum eventType : values()) {
            if (eventType.getCode().equals(code)) {
                return eventType;
            }
        }
        return null;
    }
}
