package com.xhqb.spectre.common.enums;

/**
 * 黑名单类型枚举
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
public enum BlacklistTypeEnum {
    
    /**
     * 退订上行
     */
    TD_UPLINK("td_uplink", "退订上行"),
    
    /**
     * 用户投诉
     */
    CUSTOMER_APPEAL("customer_appeal", "用户投诉"),
    
    /**
     * 其他
     */
    OTHER("other", "其他");
    
    private final String code;
    private final String name;
    
    BlacklistTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static BlacklistTypeEnum getByCode(String code) {
        for (BlacklistTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
