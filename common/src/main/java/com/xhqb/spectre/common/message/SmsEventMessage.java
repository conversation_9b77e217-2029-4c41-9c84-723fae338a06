package com.xhqb.spectre.common.message;

import com.xhqb.spectre.common.enums.SmsEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 短信事件消息
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsEventMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 事件类型
     */
    private SmsEventTypeEnum eventType;
    
    /**
     * 事件时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 事件来源
     */
    private String source;
    
    /**
     * 事件数据（JSON格式）
     */
    private String eventData;
    
    /**
     * 创建黑名单事件消息
     */
    public static SmsEventMessage createBlacklistEvent(String eventData, String source) {
        return SmsEventMessage.builder()
                .eventType(SmsEventTypeEnum.BLACKLIST)
                .eventTime(LocalDateTime.now())
                .eventId(generateEventId())
                .source(source)
                .eventData(eventData)
                .build();
    }
    
    /**
     * 创建退订事件消息
     */
    public static SmsEventMessage createUnsubscribeEvent(String eventData, String source) {
        return SmsEventMessage.builder()
                .eventType(SmsEventTypeEnum.UNSUBSCRIBE)
                .eventTime(LocalDateTime.now())
                .eventId(generateEventId())
                .source(source)
                .eventData(eventData)
                .build();
    }
    
    /**
     * 生成事件ID
     */
    private static String generateEventId() {
        return System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
