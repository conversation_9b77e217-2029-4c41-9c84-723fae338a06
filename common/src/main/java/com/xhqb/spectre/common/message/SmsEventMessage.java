package com.xhqb.spectre.common.message;

import com.xhqb.spectre.common.enums.SmsEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsEventMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    private SmsEventTypeEnum eventType;
    private LocalDateTime eventTime;
    private String eventId;
    private String source;
    private String eventData; // JSON格式

    public static SmsEventMessage createBlacklistEvent(String eventData, String source) {
        return SmsEventMessage.builder()
                .eventType(SmsEventTypeEnum.BLACKLIST)
                .eventTime(LocalDateTime.now())
                .eventId(generateEventId())
                .source(source)
                .eventData(eventData)
                .build();
    }

    public static SmsEventMessage createUnsubscribeEvent(String eventData, String source) {
        return SmsEventMessage.builder()
                .eventType(SmsEventTypeEnum.UNSUBSCRIBE)
                .eventTime(LocalDateTime.now())
                .eventId(generateEventId())
                .source(source)
                .eventData(eventData)
                .build();
    }

    private static String generateEventId() {
        return System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
