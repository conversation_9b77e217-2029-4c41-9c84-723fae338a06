package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.PhoneInfoDO;
import com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface PhoneNumberStatusMapper {

    int insert(PhoneNumberStatusDO record);

    int insertSelective(PhoneInfoDO record);

    PhoneNumberStatusDO selectByPrimaryKey(Long id);

    List<PhoneNumberStatusDO> selectByMobiles(@Param("mobiles") Set<String> mobiles,
                                              @Param("timestamp") Integer timestamp);

    PhoneNumberStatusDO selectByMobile(String mobile);

    int updateByPrimaryKeySelective(PhoneNumberStatusDO record);

    int updateByPrimaryKey(PhoneNumberStatusDO record);

    void saveOrUpdateBatch(List<PhoneNumberStatusDO> records);

    List<String> selectEmptyNumbers(@Param("emptyCount") int emptyCount);
}




