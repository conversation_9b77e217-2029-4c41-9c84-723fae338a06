package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.constant.MobileBlackConstant;
import com.xhqb.spectre.api.exception.GlobalException;
import com.xhqb.spectre.api.model.entity.BlackAddDTO;
import com.xhqb.spectre.api.model.smsreq.MobileBlackDTO;
import com.xhqb.spectre.api.model.smsresp.MobileBlackVO;
import com.xhqb.spectre.api.service.MobileBlackService;
import com.xhqb.spectre.api.utils.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.mapper.MobileBlackMapper;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;
import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.common.service.SmsEventService;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@Service
@Slf4j
public class MobileBlackServiceImpl implements MobileBlackService {
    @Resource
    private MobileBlackMapper mobileBlackMapper;


    private static final String OPERATOR = "system";

    @Autowired
    private OpTimeMapper opTimeMapper;

    @Autowired
    @Qualifier("apiSmsEventService")
    private SmsEventService smsEventService;

    /**
     * 分页查询黑名单
     *
     * @param mobileBlackQuery
     * @return
     */
    @Override
    public CommonPager<MobileBlackVO> listByPage(MobileBlackQuery mobileBlackQuery) {
        return PageResultUtils.result(
                () -> mobileBlackMapper.countByQuery(mobileBlackQuery),
                () -> mobileBlackMapper.selectByQuery(mobileBlackQuery)
                        .stream()
                        .map(item -> MobileBlackVO.buildMobileBlackVO(item, true))
                        .collect(Collectors.toList())
        );
    }

    /**
     * 查询黑名单信息
     *
     * @param id
     * @return
     */
    @Override
    public MobileBlackVO getById(Integer id) {
        MobileBlackDO mobileBlackDO = validateAndSelectById(id);
        return MobileBlackVO.buildMobileBlackVO(mobileBlackDO, false);
    }

    /**
     * 添加黑名单
     *
     * @param addDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(MobileBlackDTO addDTO) {
        //校验
        checkAddParam(addDTO);

        //写入黑名单数据
        List<String> smsTypeCodeList = addDTO.getSmsTypeCodeList();
        String mobile = addDTO.getMobile();
        String operator = addDTO.getOperator();
        if (StringUtils.isBlank(operator)) {
            operator = OPERATOR;
        }
        final String currentUser = operator;
        smsTypeCodeList.forEach(smsTypeCode -> {
            MobileBlackDO exist = mobileBlackMapper.selectByMobileAndSmsType(mobile, smsTypeCode);
            if (Objects.isNull(exist)) {
                MobileBlackDO tmpDO = buildMobileBlackDO(addDTO, smsTypeCode, currentUser, 0);
                mobileBlackMapper.insertSelective(tmpDO);

                sendBlacklistAddEvent(mobile, smsTypeCode, addDTO.getDescription(), currentUser);
            } else {
                if (isManualAdd(exist)) {
                    //人工添加的黑名单，才能覆盖
                    MobileBlackDO tmpDO = buildMobileBlackDO(addDTO, smsTypeCode, currentUser, exist.getId());
                    mobileBlackMapper.updateByPrimaryKeySelective(tmpDO);

                    sendBlacklistAddEvent(mobile, smsTypeCode, addDTO.getDescription(), currentUser);
                }
            }
        });

        opTimeMapper.updateOpTime(OpLogConstant.MODULE_MOBILE_BLACK, DateUtil.getNow());
    }

    /**
     * 删除黑名单
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(Integer id) {
        MobileBlackDO mobileBlackDO = validateAndSelectById(id);
        if (!isManualAdd(mobileBlackDO)) {
            throw new GlobalException("非人工添加的黑名单，不能删除");
        }

        //删除记录
        mobileBlackMapper.delete(id, OPERATOR);

        sendBlacklistRemoveEvent(mobileBlackDO.getMobile(), mobileBlackDO.getSmsTypeCode(), "API删除", OPERATOR);

        opTimeMapper.updateOpTime(OpLogConstant.MODULE_MOBILE_BLACK, DateUtil.getNow());
    }

    /**
     * 批量删除黑名单
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchDelete(List<Integer> idList) {
        //校验
        if (CollectionUtils.isEmpty(idList)) {
            throw new GlobalException("待删除的黑名单ID不能为空");
        }
        List<MobileBlackDO> mobileBlackDOList = mobileBlackMapper.selectByIdList(idList);
        Map<Integer, MobileBlackDO> map = mobileBlackDOList.stream()
                .collect(Collectors.toMap(MobileBlackDO::getId, Function.identity()));
        idList.forEach(id -> {
            if (!map.containsKey(id)) {
                throw new GlobalException("未找到黑名单，ID：" + id);
            }
            if (!isManualAdd(map.get(id))) {
                throw new GlobalException("非人工添加的黑名单，不能删除，ID：" + id);
            }
        });

        //批量删除
        mobileBlackMapper.deleteByIdList(idList, OPERATOR);

        idList.forEach(id -> {
            MobileBlackDO mobileBlackDO = map.get(id);
            sendBlacklistRemoveEvent(mobileBlackDO.getMobile(), mobileBlackDO.getSmsTypeCode(), "API批量删除", OPERATOR);
        });

    }

    @Override
    public Object createBlackListEntry(BlackAddDTO blackAddDTO) {
        MobileBlackDO existingEntry = mobileBlackMapper.selectByMobileAndSmsType(blackAddDTO.getMobile(), blackAddDTO.getType());
        //是否已加入黑名单客户
        boolean existTag = false;

        if (Objects.nonNull(existingEntry)) {
           // 更新
            MobileBlackDO mobileBlackDO = buildMobileBlackDO(blackAddDTO, existingEntry.getId());
            mobileBlackMapper.updateByPrimaryKeySelective(mobileBlackDO);
            existTag = true;

            sendBlacklistAddEvent(blackAddDTO.getMobile(), blackAddDTO.getType(), blackAddDTO.getBlackReason(), blackAddDTO.getOperator());
            return existTag;
        }
        MobileBlackDO newEntry = buildMobileBlackDO(blackAddDTO, 0);
        mobileBlackMapper.insertSelective(newEntry);
        opTimeMapper.updateOpTime(OpLogConstant.MODULE_MOBILE_BLACK, DateUtil.getNow());

        sendBlacklistAddEvent(blackAddDTO.getMobile(), blackAddDTO.getType(), blackAddDTO.getBlackReason(), blackAddDTO.getOperator());
        return existTag;
    }

    private MobileBlackDO validateAndSelectById(Integer id) {
        MobileBlackDO mobileBlackDO = mobileBlackMapper.selectByPrimaryKey(id);
        if (Objects.isNull(mobileBlackDO)) {
            throw new GlobalException("未找到该黑名单");
        }
        return mobileBlackDO;
    }

    private void checkAddParam(MobileBlackDTO mobileBlackDTO) {
        //参数格式校验
        ValidatorUtil.validate(mobileBlackDTO);

        //短信类型校验
        mobileBlackDTO.getSmsTypeCodeList().forEach(item -> {
            if (!MessageTypeEnum.contains(item)) {
                throw new GlobalException("不支持该短信类型编码：" + item);
            }
        });
    }

    private MobileBlackDO buildMobileBlackDO(MobileBlackDTO addDTO, String smsTypeCode, String currentUser, Integer id) {
        Date expiredTime = StringUtils.isBlank(addDTO.getExpiredTime()) ? null : cn.hutool.core.date.DateUtil.parseDateTime(addDTO.getExpiredTime());
        MobileBlackDO mobileBlackDO = MobileBlackDO.builder()
                .cid(addDTO.getCid())
                .mobile(addDTO.getMobile())
                .source(addDTO.getSource())
                .smsTypeCode(smsTypeCode)
                .description(addDTO.getDescription())
                .addType(MobileBlackConstant.ADD_TYPE_MANUAL)
                .updater(currentUser)
                .appCode("")
                .expiredTime(expiredTime)
                .build();
        if (id > 0) {
            //更新
            mobileBlackDO.setId(id);
        } else {
            //新增
            mobileBlackDO.setCreator(currentUser);
        }
        return mobileBlackDO;
    }


    private MobileBlackDO buildMobileBlackDO(BlackAddDTO addDTO, int id) {
        Date expiredTime = StringUtils.isBlank(addDTO.getExpirationTime()) ? null : cn.hutool.core.date.DateUtil.parseDateTime(addDTO.getExpirationTime());
        MobileBlackDO mobileBlackDO = MobileBlackDO.builder()
                .cid(addDTO.getCid())
                .mobile(addDTO.getMobile())
                .source(addDTO.getSource())
                .smsTypeCode(addDTO.getType())
                .description(addDTO.getBlackReason())
                .addType(MobileBlackConstant.ADD_TYPE_MANUAL)
                .updater(addDTO.getOperator())
                .expiredTime(expiredTime)
                .build();

        if (id > 0) {
            //更新
            mobileBlackDO.setId(id);
        } else {
            //新增
            mobileBlackDO.setCreator(addDTO.getOperator());
        }
        return mobileBlackDO;
    }

    private boolean isManualAdd(MobileBlackDO mobileBlackDO) {
        return mobileBlackDO.getAddType().equals(MobileBlackConstant.ADD_TYPE_MANUAL);
    }

    private String buildCacheValue(String smsTypeCode, String mobile) {
        return smsTypeCode + "_" + mobile;
    }

    public Stream<MobileBlackDO> selectAllByLastRefresh(@Nullable Integer lastRefreshTime,
                                                        int pageSize) {

        Date updateTime = DateUtil.intToDate(lastRefreshTime);
        return StreamSupport.stream(new Spliterators.AbstractSpliterator<MobileBlackDO>(
                Long.MAX_VALUE, Spliterator.ORDERED | Spliterator.NONNULL) {
            private int lastId = 0;
            private Iterator<MobileBlackDO> currentBatch = Collections.emptyIterator();

            @Override
            public boolean tryAdvance(Consumer<? super MobileBlackDO> action) {
                if (!currentBatch.hasNext()) {
                    List<MobileBlackDO> nextBatch = mobileBlackMapper.selectBlackListAfterId(lastId, updateTime, pageSize);
                    if (CollectionUtils.isEmpty(nextBatch)) {
                        return false;
                    }
                    currentBatch = nextBatch.iterator();
                    lastId = nextBatch.get(nextBatch.size() - 1).getId();
                }
                action.accept(currentBatch.next());
                return true;
            }
        }, false);
    }

    private void sendBlacklistAddEvent(String mobile, String smsTypeCode, String reason, String operator) {
        try {
            smsEventService.sendBlacklistEvent(
                mobile,
                BlacklistActionEnum.ADD,
                BlacklistTypeEnum.OTHER,
                smsTypeCode,
                reason,
                operator,
                "api-external-add"
            );
        } catch (Exception e) {
            log.error("发送加入黑名单事件失败: mobile={}, smsType={}, error={}", mobile, smsTypeCode, e.getMessage(), e);
        }
    }

    private void sendBlacklistRemoveEvent(String mobile, String smsTypeCode, String reason, String operator) {
        try {
            smsEventService.sendBlacklistEvent(
                mobile,
                BlacklistActionEnum.REMOVE,
                BlacklistTypeEnum.OTHER,
                smsTypeCode,
                reason,
                operator,
                "api-external-remove"
            );
        } catch (Exception e) {
            log.error("发送退出黑名单事件失败: mobile={}, smsType={}, error={}", mobile, smsTypeCode, e.getMessage(), e);
        }
    }
}
