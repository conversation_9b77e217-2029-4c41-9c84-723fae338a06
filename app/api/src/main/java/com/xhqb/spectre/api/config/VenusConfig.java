package com.xhqb.spectre.api.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/10/11
 */
@Component
@Data
public class VenusConfig {

    /**
     * 短链编码的长度
     */
    @Value("${short-url.code.length:4}")
    private Integer shortCodeLength;

    /**
     * 短链域名
     */
    @Value("${short-url.domain:xh1.cn}")
    private String shortUrlDomain;

    @Value("${zbx-short-url.code.length:6}")
    private Integer zbxShortCodeLength;

    @Value("${zbx-short-url.domain:zbx1.cn}")
    private String zbxShortUrlDomain;

    /**
     * 对外开放app code
     */
    @Value("#{'${openapi.appcodes:}'.split(',')}")
    private Set<String> openApiAppCodes;

    /**
     * openapi aes key
     */
    @Value("${openapi.aes.key}")
    private String openApiAesKey;

    @Value("${spectre.api.channel.api.test.enable:false}")
    private boolean channelApiTestEnable;

    @Value("#{'${spectre.api.channel.api.test.tplCodes:cmpp_105525_tpl}'.split(',')}")
    private List<String> channelApiTestTplCodes;


    /**
     * 默认三天
     */
    @Value("${spectre.api.n.ago.day:3}")
    private Integer nAgoDay;

    @Value("${sharding.sql.print.enable:false}")
    private String sqlPrintEnable;

    /**
     * 2024-01-31 23:59:59
     */
    @Value("${sharding.start.time.millis:1706716799000}")
    private long shardingStartTimeMillis;

    @Value("${aliyun.dytnsapi.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.dytnsapi.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.dytnsapi.authCode:IU2UgFW0x9}")
    private String authCode;

    @Value("${aliyun.dytnsapi.mask:NORMAL}")
    private String mask;

    @Value("${aliyun.client.connectionTimeout:5}")
    private int connectionTimeout;

    @Value("${aliyun.client.responseTimeout:3}")
    private int responseTimeout;

    @Value("${aliyun.client.maxConnections:64}")
    private int maxConnections;

    @Value("${aliyun.client.maxIdleTimeOut:60}")
    private int maxIdleTimeOut;

    @Value("${aliyun.consumer.per.num:30}")
    private int perNum;

    @Value("${phone.number.status.expired:2592000}")
    private Integer phoneNumberStatusExpired;

    @Value("${phone.number.status.limit:140.0}")
    private double phoneNumberStatusLimit;

    @Value("${phone.number.status.realTimeNum:1}")
    private Integer realTimeNum;

    /**
     * 是否跳过短信群发黑名单检测开关,true表示开关打开, false表示开关关闭
     */
    @Value("${spectre.api.skipBlackCheckEnable:true}")
    private boolean skipBlackCheckEnable;
    /**
     * debug控制日志打印
     */
    @Value("${spectre.api.debug:false}")
    private boolean debug;

    /**
     * 债转短信模板编码列表,多个模板编码使用逗号分割
     */
    @Value("${spectre.api.debtSmsTplCodeList:}")
    private List<String> debtSmsTplCodeList;

    /**
     * 债转短信告警间隔分钟数
     */
    @Value("${spectre.api.debtSmsRetryIntervalMinutes:5}")
    private int debtSmsRetryIntervalMinutes;


    /**
     * 营销查询默认三天
     */
    @Value("${market.query.days:3}")
    private Integer marketQueryDays;

    /**
     * 启用发送限制总数统计优化开关
     */
    @Value("${spectre.api.enableSendLimitTotalMetricOptimize:true}")
    private volatile boolean enableSendLimitTotalMetricOptimize;

    /**
     * 短链服务 url
     */
    @Value("${url-shortener.shortenerUrl:http://spectre-url-shortener/shortener}")
    private String shortenerUrl;

    /**
     * 服务节点数
     */
    @Value("${api.service.node.num:1}")
    private volatile int nodeNum;

    /**
     * 限流值
     */
    @Value("${user.shortUrl.add.limit.value:4000}")
    private volatile int limitValue;

    /**
     * 是否开启限流
     */
    @Value("${user.shortUrl.limit.enable:true}")
    private volatile boolean limitEnable;

    @Value("${user.shortUrl.query.day:30}")
    private volatile int userShortUrlQueryDay;


    /**
     * 过滤营销黑名单的通知模版列表
     */
    @Value("#{'${notice.tpl.filter.market.blacklist:}'.split(',')}")
    private Set<String> noticeTplFilterMarketBlackList;

    /**
     * 新参数校验开关
     */
    @Value("${new.validate.param.enable:true}")
    private boolean newValidateParamEnable;


    /**
     * mq最大延迟时间
     */
    @Value("${mq.max.delay.time.hours:72}")
    private volatile int mqMaxDelayTimeHours;


    /**
     * 查询订单默认查询天数
     */
    @Value("${query.order.days:7}")
    private volatile int queryOrderDays;


    /**
     * 判断短信模板是否是指定的债转短信
     *
     * @param tplCode
     * @return
     */
    public boolean isDebtSmsTplCode(String tplCode) {
        if (CollectionUtils.isEmpty(debtSmsTplCodeList)) {
            return false;
        }

        return debtSmsTplCodeList.contains(tplCode);
    }
}
