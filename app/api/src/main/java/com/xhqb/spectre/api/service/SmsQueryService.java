package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.model.entity.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.*;

import java.util.List;

public interface SmsQueryService {
    List<QueryOrderVO> queryOrder(List<String> requestIdList, String appCode);

    CommonPager<QueryTplVO> findTplListByPage(TplQuery tplQuery);

    List<TplInfoVO> queryByAppCode(String appCode);

    QueryTplVO queryTpl(String code, String signName);

    QueryTplVO getTplById(Integer id);

    CommonPager<QueryReissueOrderVO> queryReissueOrderByMobile(ReissueOrderQuery query);

    List<QueryChannelAccountVO> queryChannelAccount(ChannelAccountQuery query);

    TestStatVO queryTestStats(TestStatQuery query);

    List<QueryDebtSmsVO> queryDebtSms(DebtSmsQuery query);

    List<ChannelBindingVO> checkBindingThenGetAccount(List<TplChannelDTO> tplChannelDTOList);

    List<String> retrieveRecentEmptyNumbers(int emptyCount);

    List<ChannelVO> getAllSmsChannels();

    List<String> smsContentByTplCodeAndCount(String tplCode, Integer count);
}
