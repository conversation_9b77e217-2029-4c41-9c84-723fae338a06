package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.service.FailResendStrategyService;
import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import com.xhqb.spectre.common.dal.entity.FailResendStrategyDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.mapper.FailResendRuleMapper;
import com.xhqb.spectre.common.dal.mapper.FailResendStrategyMapper;
import com.xhqb.spectre.common.dto.FailResendMatchResultDTO;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategyFactory;
import com.xhqb.spectre.common.utils.FailResendStrategyCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 补发策略
 */
@Service
@Slf4j
public class FailResendStrategyServiceImpl implements FailResendStrategyService {
    
    @Resource
    private FailResendStrategyMapper failResendStrategyMapper;
    
    @Resource
    private FailResendRuleMapper failResendRuleMapper;
    
    @Resource
    private FailResendSceneStrategyFactory strategyFactory;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    private static final String CACHE_VERSION_KEY = "spectre:fail-resend-strategy:version";

    private volatile String localVersion = null;

    @PostConstruct
    @Override
    public void initCache() {
        try {
            List<FailResendStrategyDO> strategies = failResendStrategyMapper.selectAllEnabled();
            List<FailResendRuleDO> rules = failResendRuleMapper.selectAllEnabled();
            FailResendStrategyCache.initCache(strategies, rules, strategyFactory);

        } catch (Exception e) {
            log.error("初始化补发策略缓存失败", e);
        }
    }


    @Override
    public boolean isTplInStrategy(String tplCode) {
        return FailResendStrategyCache.isTplInStrategy(tplCode);
    }

    @Override
    public FailResendMatchResultDTO matchStrategy(SmsOrderDO smsOrderDO) {
        checkAndRefreshCache();
        return FailResendStrategyCache.matchStrategy(smsOrderDO);
    }
    
    /**
     * 检查版本号并刷新缓存
     */
    private void checkAndRefreshCache() {
        try {
            String redisVersion = stringRedisTemplate.opsForValue().get(CACHE_VERSION_KEY);
            
            // 如果Redis中没有版本号，则生成新的版本号并写入Redis
            if (redisVersion == null || redisVersion.isEmpty()) {
                log.info("Redis中版本号为空，生成新的版本号并写入Redis");
                redisVersion = generateAndSetRedisVersion();
            }

            if (localVersion == null || !localVersion.equals(redisVersion)) {
                log.info("Redis版本号与本地版本号不一致，刷新本地缓存: localVersion={}, redisVersion={}", localVersion, redisVersion);
                initCache();
                localVersion = redisVersion;
                log.info("本地缓存刷新完成，{}", FailResendStrategyCache.getCacheStats());
            }
        } catch (Exception e) {
            log.error("检查版本号失败", e);
        }
    }

    private String generateAndSetRedisVersion() {
        String newVersion = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        stringRedisTemplate.opsForValue().set(CACHE_VERSION_KEY, newVersion, 24, TimeUnit.HOURS);
        return newVersion;
    }
    
    @Override
    public void refreshCache() {
        log.info("更新补发策略缓存版本号");
        String version = generateAndSetRedisVersion();
        log.info("补发策略缓存版本号已更新: {}", version);
    }


}