package com.xhqb.spectre.logserver.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusHistogramMetrics;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.common.constant.CodeMappingConstants;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.dto.mq.DeliverResqDTO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.common.enums.ProtocolTypeEnum;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.logserver.cache.CodeMappingCache;
import com.xhqb.spectre.logserver.dto.ChannelErrCodeDTO;
import com.xhqb.spectre.logserver.dto.CmppRedisDTO;
import com.xhqb.spectre.logserver.dto.RetryDTO;
import com.xhqb.spectre.logserver.enums.GatewayEnum;
import com.xhqb.spectre.logserver.service.*;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.MessageId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Objects;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/9/27
 */
@Service
@Slf4j
public class DeliverResqServiceImpl implements DeliverResqService {

    @Resource
    private OrderSaveBatchService orderSaveBatchService;

    @Resource
    private ReceiptSaveBatchService receiptSaveBatchService;

    @Resource
    private SendToCmppService sendToCmppService;

    @Resource
    private RetryService retryService;

    @Resource
    private RedisService redisService;

    @Resource
    private MQTemplate<String> mqTemplate;

    /**
     * deliver相同结果的分布式锁超时时间
     */
    @Value("${sms.deliver.lock.second:30}")
    private Long deliverLockTime;

    /**
     * 补发mq, spectre-fail-resend
     */
    @Value("${tdmq.producers.failResend}")
    private String failResendTopic;

    /**
     * 补发mq开关
     */
    @Value("${spectre.fail.resend.enable:true}")
    private boolean failResendEnable;

    /**
     * 补发消息延迟时间（单位：秒）
     */
    @Value("${spectre.fail.resend.delay.second:120}")
    private long failResendDelaySecond;

    /**
     * 短信重发的线程池
     */
    @Resource(name = "retryThreadPool")
    private ThreadPoolExecutor retryThreadPool;

    private static final String SUCCESS_CODE = "DELIVRD";

    private ChannelErrCodeDTO channelErrCodeDTO = new ChannelErrCodeDTO(
            CodeMappingConstants.XH_CODE_DEFAULT,
            CodeMappingConstants.XH_DESC_DEFAULT,
            CodeMappingConstants.ERR_CODE_RETRY);

    /**
     * get方法
     *
     * @return ChannelErrCodeDTO
     */
    public ChannelErrCodeDTO getChannelErrCodeDTO() {
        return channelErrCodeDTO;
    }

    /**
     * set方法
     *
     * @param channelErrCodeDTO 渠道错误码映射实体类
     */
    public void setChannelErrCodeDTO(ChannelErrCodeDTO channelErrCodeDTO) {
        this.channelErrCodeDTO = channelErrCodeDTO;
    }

    /**
     * 回执计数
     */
    private static final Counter COUNTER = new PrometheusCounterMetrics("spectre_smslogserver_deliver_total", "description")
            .createWithLabels("protocol_type", "deliver_type", "sms_type", "channel_code");
    /**
     * 间隔时间计数
     */
    private static final Histogram SUBTIME = new PrometheusHistogramMetrics("spectre_smslogserver_recvSendTime_recvReportTime", "histogram description")
            .createWithLabels("sms_type", "channel_code");

    /**
     * 回执入库
     *
     * @param deliverResqDTO deliverResqDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateReceipt(DeliverResqDTO deliverResqDTO) {
        if (!ProtocolTypeEnum.CMPP.getName().equals(deliverResqDTO.getType())) {
            return;
        }
        CmppDeliverResqDTO cmppDeliverResqDTO = deliverResqDTO.getCmppDeliverResqDTO();
        if (Objects.isNull(cmppDeliverResqDTO)) {
            log.info("cmppDeliverResqDTO无数据");
            return;
        }
        String smsTypeCode = cmppDeliverResqDTO.getSmsTypeCode();
        String channelCode = cmppDeliverResqDTO.getChannelCode();
        Long recvReportTime = cmppDeliverResqDTO.getRecvReportTime();
        Integer recvSendTime = cmppDeliverResqDTO.getRecvSendTime();
        // 埋点计数, 回执次数
        if (SUCCESS_CODE.equals(cmppDeliverResqDTO.getReportStatus())) {
            COUNTER.labels("cmpp", "delivrd", smsTypeCode, channelCode).inc();
        } else {
            COUNTER.labels("cmpp", "other", smsTypeCode, channelCode).inc();
        }
        // 间隔时间
        if (recvReportTime != null && recvSendTime != null) {
            SUBTIME.labels(smsTypeCode, channelCode).observe((recvReportTime - recvSendTime) / 1000);
        }
        // 入库
        save(deliverResqDTO.getCmppDeliverResqDTO());
    }

    /**
     * cmpp回执入库
     *
     * @param cmppRecord CmppDeliverResqDTO
     */
    @Override
    public void save(CmppDeliverResqDTO cmppRecord) {
        //保存deliver结果记录
        SmsReceiptDO smsReceiptDO = cmppToReceipt(cmppRecord);
//        log.info("cmpp回执入库; sendReceiptDO={}", smsReceiptDO);
        receiptSaveBatchService.saveReceiptBatch(smsReceiptDO);

        //判断deliver结果是否重复，重复则忽略（长短信会拆分成几个相同的deliver结果，相同的结果无需多次更新订单表）
        String lockKey = MessageFormat.format(RedisKeys.LogServerKeys.DELIVER_RESULT_LOCK_KEY,
                String.valueOf(cmppRecord.getOrderId()), cmppRecord.getResend(), cmppRecord.getReportStatus());
        try {
            Boolean getLock = redisService.lock(lockKey, "1", deliverLockTime, TimeUnit.SECONDS);
            if (Boolean.FALSE.equals(getLock)) {
                //获取锁失败，说明deliver结果重复，之前已处理过相同逻辑，直接返回
                return;
            }
        } catch (RejectedExecutionException rje) {
            log.warn("redis断开; msg={}", rje.getMessage());
            return;
        }

        //更新订单表
        SmsOrderDO updateSmsOrderDO = SmsOrderDO.builder()
                .orderId(cmppRecord.getOrderId())
                .resend(cmppRecord.getResend())
                .channelMsgId(smsReceiptDO.getChannelMsgId())
                .reportTime(smsReceiptDO.getRecvReportTime())
                .reportStatus(smsReceiptDO.getStatus())
                .reportCode(smsReceiptDO.getReportStatus())
                .reportDesc(smsReceiptDO.getReportDesc())
                .submitTime(smsReceiptDO.getSubmitTime())
                .doneTime(smsReceiptDO.getDoneTime())
                .tableNameSuffix(cmppRecord.getTableNameSuffix())
                .build();
//        log.info("更新短信订单表回执状态，smsOrderDO={}", updateSmsOrderDO);
        orderSaveBatchService.saveOrUpdateOrderReport(updateSmsOrderDO);

        //短信重发，提交到线程池处理
        retryThreadPool.execute(() -> retry(cmppRecord));
    }

    /**
     * CmppDeliverResqDTO to SendReceiptDO
     *
     * @param bean CmppDeliverResqDTO
     * @return SendReceiptDO
     */
    @Override
    public SmsReceiptDO cmppToReceipt(CmppDeliverResqDTO bean) {
        SmsReceiptDO smsReceiptDO = new SmsReceiptDO();
        smsReceiptDO.setOrderId(bean.getOrderId());
        smsReceiptDO.setChannelAccountId(bean.getChannelAccountId() == null ? 0 : bean.getChannelAccountId());
        smsReceiptDO.setTplCode(bean.getTplCode() == null ? "" : bean.getTplCode());
        smsReceiptDO.setChannelMsgId(bean.getChannelMsgId() == null ? "" : bean.getChannelMsgId());
        smsReceiptDO.setMobile(bean.getMobile() == null ? "" : bean.getMobile());
//        smsReceiptDO.setSubmitTime(bean.getRecvSendTime() == null ? 0 : bean.getRecvSendTime());
        smsReceiptDO.setSubmitTime(bean.getSubmitTime() == null ? 0 : DateUtil.strToIntTime(bean.getSubmitTime()));
        smsReceiptDO.setRecvReportTime(bean.getRecvReportTime() == null ? 0 : new BigDecimal(bean.getRecvReportTime()).intValue());
        smsReceiptDO.setDoneTime(bean.getDoneTime() == null ? 0 : DateUtil.strToIntTime(bean.getDoneTime()));
        smsReceiptDO.setDestTerminalId(bean.getDestTerminalId() == null ? "" : bean.getDestTerminalId());
        smsReceiptDO.setChannelCode("");
        smsReceiptDO.setReportStatus("");
        smsReceiptDO.setStatus(getChannelErrCodeDTO().getXhErrCode());
        smsReceiptDO.setReportDesc(getChannelErrCodeDTO().getCodeDesc());
        if (bean.getChannelCode() != null && bean.getReportStatus() != null) {
            ChannelErrCodeDTO deliveryStatus = getDeliveryStatus(bean.getChannelCode(), bean.getReportStatus());
            setChannelErrCodeDTO(deliveryStatus);
            smsReceiptDO.setChannelCode(bean.getChannelCode());
            smsReceiptDO.setReportStatus(bean.getReportStatus());
            smsReceiptDO.setStatus(deliveryStatus.getXhErrCode());
            smsReceiptDO.setReportDesc(deliveryStatus.getCodeDesc());
        }
        return smsReceiptDO;
    }

    /**
     * 关系映射
     *
     * @param channelCode 渠道code
     * @param status      状态码
     * @return 映射关系
     */
    private ChannelErrCodeDTO getDeliveryStatus(String channelCode, String status) {
        // 从渠道code中查找
        ChannelErrCodeDTO channelErrCodeDTO = CodeMappingCache.getInstance().getErrCode(channelCode, CodeMappingConstants.TYPE_DELIVER, status);
        if (channelErrCodeDTO != null) {
            return channelErrCodeDTO;
        }
        // 从默认中查找
        channelErrCodeDTO = CodeMappingCache.getInstance().getErrCode(CodeMappingConstants.CHANNEL_CODE_DEFAULT, CodeMappingConstants.TYPE_DELIVER, status);
        if (channelErrCodeDTO != null) {
            return channelErrCodeDTO;
        }
        // 返回[404,"未知错误码"]
        return new ChannelErrCodeDTO(CodeMappingConstants.XH_CODE_DEFAULT, CodeMappingConstants.XH_DESC_DEFAULT, CodeMappingConstants.ERR_CODE_RETRY);
    }

    /**
     * 短信重发
     *
     * @param cmppRecord
     */
    private void retry(CmppDeliverResqDTO cmppRecord) {
        Long subTime = Math.abs(cmppRecord.getRecvReportTime() - cmppRecord.getRecvSendTime());
        RetryDTO retryDTO = RetryDTO.builder()
                .type(CodeMappingConstants.TYPE_DELIVER)
                .orderId(cmppRecord.getOrderId())
                .channelErrCodeDTO(getChannelErrCodeDTO())
                .channelStatus(cmppRecord.getReportStatus())
                .resend(cmppRecord.getResend())
                .smsTypeCode(cmppRecord.getSmsTypeCode())
                .subTime(subTime)
                .reqSrc(cmppRecord.getReqSrc())
                .gatewayUserName(cmppRecord.getGatewayUserName())
                .build();
        Boolean notNeedRetry = retryService.retry(retryDTO);
        // 状态回推cmpp逻辑
        if (ProtocolTypeEnum.CMPP.getValue().equals(cmppRecord.getReqSrc()) && notNeedRetry) {
            // 如果短信不重发，表明走到终态，发送redis，并且发送udp包
            CmppRedisDTO cmppRedisDTO = CmppRedisDTO.builder()
                    .requestId(cmppRecord.getRequestId() == null ? "" : cmppRecord.getRequestId())
                    .gatewayUserName(cmppRecord.getGatewayUserName() == null ? "" : cmppRecord.getGatewayUserName())
                    .msgStatus(GatewayEnum.DONE.getMessageStatus())
                    .reportStatus(cmppRecord.getReportStatus())
                    .submitTime(cmppRecord.getSubmitTime())
                    .doneTime(cmppRecord.getDoneTime())
                    .destterminalId(cmppRecord.getDestTerminalId() == null ? "" : cmppRecord.getDestTerminalId())
                    .destId(cmppRecord.getDestId() == null ? "" : cmppRecord.getDestId())
                    .build();
            sendToCmppService.saveRedis(cmppRedisDTO);

        }

        if (ProtocolTypeEnum.HTTP.getValue().equals(cmppRecord.getReqSrc())
                && notNeedRetry
                && failResendEnable
                && CodeMappingConstants.TYPE_DELIVER.equals(retryDTO.getType())
                && !CodeMappingConstants.DELIVER_CHANNEL_CODE_SUCCESS.equals(retryDTO.getChannelStatus())) {
            sendFailResendMessage(cmppRecord);
        }

    }

    /**
     * 发送补发MQ消息
     *
     * @param cmppRecord
     */
    private void sendFailResendMessage(CmppDeliverResqDTO cmppRecord) {
        try {
            String message = JSONObject.toJSONString(cmppRecord);
            if (failResendDelaySecond > 0) {
                MessageId messageId = mqTemplate.createMessage(failResendTopic, message)
                        .deliverAfter(failResendDelaySecond, TimeUnit.SECONDS)
                        .send();
                log.debug("发送补发MQ消息成功, messageId: {}, orderId: {}, delay: {}",
                        messageId, cmppRecord.getOrderId(), failResendDelaySecond);
            } else {
                MessageId messageId = mqTemplate.send(failResendTopic, message);
                log.debug("发送补发MQ消息成功, messageId: {}, orderId: {}",
                        messageId, cmppRecord.getOrderId());
            }
        } catch (Exception e) {
            log.warn("发送补发MQ消息异常, orderId: {}", cmppRecord.getOrderId(), e);
        }
    }
}
